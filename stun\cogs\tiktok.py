import os
import re
import json
import discord
import datetime
from io import BytesIO
from typing import Annotated, Optional
from bs4 import BeautifulSoup

from discord import Embed, app_commands
from discord.ext.commands import Cog, hybrid_group, Context

from modules.evelinabot import <PERSON><PERSON>
from modules.utilities import(
    <PERSON><PERSON><PERSON>,
    TikTokUser,
)

class TikTok(Cog):
    def __init__(self, bot: <PERSON><PERSON>):
        self.bot = bot
        self.regex = {
            "pinterest": r"https://((ro|ru|es|uk|fr|de|in|gr|www).pinterest.com/pin|pin.it)/([0-9a-zA-Z]+)",
            "youtube": r"((http(s)?:\/\/)?)(www\.)?((youtube\.com\/)|(youtu.be\/)(watch|shorts))[\S]+",
            "tiktok download": r"^.*https:\/\/(?:m|www|vm|vt)?\.?tiktok\.com\/((?:.*\b(?:(?:v|video|t)\/|\?shareId=|\&item_id=)(\d+))|\w+)",
        }
    
    @staticmethod
    def humanize_number(value):
        if value < 1000:
            return f"**{value}**"
        elif value < 1000000:
            return f"**{value/1000:.1f}k**"
        elif value < 1000000000:
            return f"**{value/1000000:.1f}m**"
        else:
            return f"**{value/1000000000:.1f}b**"

    @hybrid_group(aliases=["tt"])
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def tiktok(
        self,
        ctx: Context,
        user: Optional[Annotated[TikTokUser, Tiktok]] = None,
    ):
        """
        Get information from TikTok
        """

        if not user:
            return await ctx.send_help(ctx.command)
        else:
            return await ctx.invoke(self.bot.get_command("tiktok profile"), user=user)

    @tiktok.command(name="download", aliases=["dl"])
    async def tiktok_download(self, ctx: Context, url: str):
        """
        Download and repost a TikTok video with detailed information
        """
        tt = re.compile(self.regex["tiktok download"])

        if not tt.match(url):
            return await ctx.alert("This is not a **TikTok** post url")

        await ctx.typing()
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:129.0) Gecko/20100101 Firefox/129.0"
        }

        try:
            # Get the TikTok webpage
            html = await ctx.bot.session.get_text(url, headers=headers)

            # Parse webpage for video data
            soup = BeautifulSoup(html, "html.parser")
            script = soup.find("script", attrs={"id": "__UNIVERSAL_DATA_FOR_REHYDRATION__"})
            if not script:
                return await ctx.alert("Failed to fetch TikTok data")

            payload = json.loads(script.string)
            if not payload["__DEFAULT_SCOPE__"].get("webapp.video-detail"):
                return await ctx.alert("This TikTok cannot be downloaded now")

            # Get video details and download video
            video_info = payload["__DEFAULT_SCOPE__"]["webapp.video-detail"]["itemInfo"]["itemStruct"]
            video_url = video_info["video"]["playAddr"]

            # Download the video
            video_data = await ctx.bot.session.get_bytes(video_url, headers=headers)

            if len(video_data) > getattr(ctx.guild, "filesize_limit", 26214400):
                return await ctx.alert("Video file too large to be posted in this server")

            # Create video file for Discord to embed properly
            file = discord.File(BytesIO(video_data), filename="stuntiktok.mp4")
            author_info = video_info["author"]

            # Create embed with video info
            embed = discord.Embed(
                title=f"{author_info['nickname']} (@{author_info['uniqueId']})",
                description=video_info.get("desc", "No description"),
                color=0x000000,
                url=url
            )

            # Initialize TikTokVideoView with video data
            from modules.utilities.tiktok_views import TikTokVideoView
            view = TikTokVideoView(self.bot, ctx, video_info, video_data)

            # Send video file with embed and view - Discord will display it as embedded video
            message = await ctx.reply(embed=embed, file=file, view=view)
            view.message = message

        except Exception as e:
            return await ctx.alert(f"Failed to process TikTok: {str(e)}")

    @tiktok.command(name="profile")
    async def tiktok_profile(
        self,
        ctx: Context,
        user: Annotated[TikTokUser, Tiktok]
    ):
        """View a TikTok user's profile information"""
        try:
            # Basic statistics and user info from the user object
            followers = f"{getattr(user, 'followers', 0):,}"
            following = f"{getattr(user, 'following', 0):,}"
            likes = f"{getattr(user, 'hearts', 0):,}"
            videos = getattr(user, 'videos', 0) # Assuming 'videos' attribute exists
            friends = getattr(user, 'friends', 'N/A') # Assuming 'friends' attribute exists
            user_id = getattr(user, 'uniqueId', getattr(user, 'id', 'N/A')) # Assuming 'uniqueId' or 'id' exists
            region = getattr(user, 'region', 'N/A') # Assuming 'region' attribute exists

            # Account creation time is not in the current TikTokUser model or converter.
            # The image shows "5 years ago" (relative time).
            # We will add a placeholder field for now.
            account_creation_display = "N/A" # Placeholder

            # Create main embed with title and thumbnail
            embed = Embed(
                color=0x000000, # Or a color matching the image if possible
                url=getattr(user, 'url', f"https://tiktok.com/@{user.username}"),
                timestamp=datetime.datetime.now() # Current timestamp, can adjust if needed
            )
            embed.set_thumbnail(url=getattr(user, 'avatar', None))

            # Set author field (matching the username line in the image)
            author_name = user.username
            if getattr(user, 'verified', False):
                author_name += " ☑️" # Add verified badge if applicable

            embed.set_author(
                name=f"{author_name} on TikTok", # "username on Tiktok" from image
                icon_url=getattr(user, 'avatar', None), # Use avatar as author icon
                url=getattr(user, 'url', f"https://tiktok.com/@{user.username}") # Link author name
            )

            # Add bio to description
            if bio := getattr(user, 'bio', None):
                embed.description = bio
            else:
                embed.description = "No bio available" # Default description

            # Add inline fields for stats and basic info, arranged similar to the image
            # Row 1: Followers, Following, Likes
            embed.add_field(name="Followers", value=followers, inline=True)
            embed.add_field(name="Following", value=following, inline=True)
            embed.add_field(name="Likes", value=likes, inline=True)

            # Row 2: Videos, Friends, Verified
            embed.add_field(name="Videos", value=f"{videos:,}" if isinstance(videos, (int, float)) else videos, inline=True)
            embed.add_field(name="Friends", value=f"{friends:,}" if isinstance(friends, (int, float)) else friends, inline=True)
            embed.add_field(name="Verified", value="Yes ☑️" if getattr(user, 'verified', False) else "No", inline=True)

            # Row 3: Private, Region, ID
            embed.add_field(name="Private", value="Yes 🔒" if getattr(user, 'private', False) else "No", inline=True)
            embed.add_field(name="Region", value=region, inline=True)
            embed.add_field(name="ID", value=user_id, inline=True)

            # Row 4: Account Creation
            embed.add_field(name="Account Creation", value=account_creation_display, inline=True)

            # Add social links as a separate field if available
            social_links = []
            if insta := getattr(user, 'instagram', None):
                 social_links.append(f"[Instagram]({insta})")
            if yt := getattr(user, 'youtube', None):
                 social_links.append(f"[YouTube]({yt})")
            if twitter := getattr(user, 'twitter', None):
                 social_links.append(f"[Twitter]({twitter})")

            if social_links:
                 # The image has a linktr.ee link in the main text/description area (bio).
                 # Let's add a field for other specific social links if available.
                 embed.add_field(name="Social Links", value=" | ".join(social_links), inline=False)


            # Add footer with TikTok ID and timestamp
            # The timestamp is handled by embed.timestamp
            embed.set_footer(text=f"Tiktok Id: {user_id}") # Add TikTok ID to footer text


            return await ctx.reply(embed=embed)

        except Exception as e:
            return await ctx.alert(f"Failed to fetch TikTok profile: {str(e)}")
        
async def setup(bot: Evelina):
    await bot.add_cog(TikTok(bot))