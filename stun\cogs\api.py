import os
import re
import json
import asyncio
import aiohttp
import random
import math
import sys
import psutil
import platform
from aiohttp import web
from datetime import datetime, timedelta
import threading

import discord
from discord import Embed, Spotify
from discord.ext import commands
from discord.ext.commands import Cog, command, is_owner

from modules.styles import colors
from modules.evelinabot import <PERSON><PERSON>Context, <PERSON><PERSON>
from modules import config

def get_command_info(command):
    return {
        "name": command.qualified_name, 
        "description": command.description or command.help or "N/A",
        "permissions": command.brief or "N/A",
        "aliases": command.aliases or [],
        "category": command.cog_name.lower() if command.cog_name else "",
        "arguments": ', '.join(command.clean_params.keys()) if command.clean_params else 'N/A',
    }

def get_commands_info(commands_list):
    commands_info = []
    excluded_cogs = []
    def add_subcommands(subcommands):
        for subcommand in subcommands:
            commands_info.append(get_command_info(subcommand))
            if isinstance(subcommand, commands.Group):
                add_subcommands(subcommand.commands)
    for command in commands_list:
        if command.cog_name and command.cog_name.lower() in excluded_cogs:
            continue
        commands_info.append(get_command_info(command))
        if isinstance(command, commands.Group):
            add_subcommands(command.commands)
    return commands_info

async def get_used_card(bot, user_id: int, business: str):
    """Get the card used by a user for a specific business"""
    card_used = await bot.db.fetchrow("SELECT * FROM economy_cards_used WHERE user_id = $1 AND business = $2", user_id, business)
    if card_used:
        card_user = await bot.db.fetchrow("SELECT * FROM economy_cards_user WHERE user_id = $1 AND card_id = $2", user_id, card_used["card_id"])
        if card_user:
            card_info = await bot.db.fetchrow("SELECT * FROM economy_cards WHERE id = $1", card_user["id"])
            if card_info:
                name = card_info["name"]
                stars = card_info["stars"]
                storage = card_user["storage"]
                multiplier = card_user["multiplier"]
                return name, stars, storage, multiplier
    return None, None, None, None

async def calculate_lab_earnings_and_upgrade(bot, user_id: int, upgrade_state: int):
    """Calculate lab earnings and upgrade costs"""
    hours = 6
    multiplier = 1
    card_used = await bot.db.fetchrow("SELECT * FROM economy_cards_used WHERE user_id = $1 AND business = $2", user_id, "lab")
    if card_used:
        card_user = await bot.db.fetchrow("SELECT * FROM economy_cards_user WHERE user_id = $1 AND card_id = $2", user_id, card_used["card_id"])
        if card_user:
            card_info = await bot.db.fetchrow("SELECT * FROM economy_cards WHERE id = $1", card_user["id"])
            if card_info:
                hours = card_user["storage"]
                multiplier = card_user["multiplier"]
    base_earnings_per_hour = 10000 + (5000 * upgrade_state)
    earnings_multiplier = 1 + (0.1 * upgrade_state)
    earnings_per_hour = base_earnings_per_hour * earnings_multiplier
    earnings_per_hour *= multiplier
    earnings_cap = earnings_per_hour * hours
    next_upgrade_cost = (base_earnings_per_hour * 24) * (1 + 0.1 * upgrade_state)
    return earnings_per_hour, earnings_cap, next_upgrade_cost

async def calculate_business_earning(bot, user_id: int, last_collected, hourrevenue):
    """Calculate business earnings based on time elapsed"""
    earnings = 0
    hours = 6
    multiplier = 1
    card_used = await bot.db.fetchrow("SELECT * FROM economy_cards_used WHERE user_id = $1 AND business = $2", user_id, "business")
    if card_used:
        card_user = await bot.db.fetchrow("SELECT * FROM economy_cards_user WHERE user_id = $1 AND card_id = $2", user_id, card_used["card_id"])
        if card_user:
            card_info = await bot.db.fetchrow("SELECT * FROM economy_cards WHERE id = $1", card_user["id"])
            if card_info:
                hours = card_user["storage"]
                multiplier = card_user["multiplier"]
    time_difference = datetime.now().timestamp() - last_collected
    if time_difference >= 3600:
        earnings = hourrevenue * (time_difference // 3600)
    earnings_cap = hourrevenue * hours
    if earnings > earnings_cap:
        earnings = earnings_cap
    earnings *= multiplier
    return earnings

def validate_api_key(key: str) -> bool:
    """Validate API key - matches the JS implementation"""
    return key == 'X3pZmLq82VnHYTd6Cr9eAw'

@web.middleware
async def api_key_middleware(request, handler):
    """Middleware to validate API key for protected endpoints"""
    # Skip validation for existing bot endpoints and static files
    exempt_paths = ['/shards', '/commands', '/team', '/templates', '/feedback', '/avatars', '/avatar', '/history', '/featured-communities', '/test-exempt', '/health', '/stats']
    exempt_prefixes = ['/user/', '/avatar/']

    # Check if path is exempt
    if (request.path in exempt_paths or
        any(request.path.startswith(prefix) for prefix in exempt_prefixes)):
        return await handler(request)

    # Check for API key in query parameters
    key = request.query.get('key')
    if not key:
        return web.json_response({'error': '400', 'message': 'Parameter "key" is required'}, status=400)

    if not validate_api_key(key):
        return web.json_response({'error': '403', 'message': 'Invalid or unauthorized API key'}, status=403)

    return await handler(request)

class Api(Cog):
    def __init__(self, bot: Evelina):
        self.bot = bot
        self.last_save = datetime.min
        self.banners = {}
        self.decorations = {}

        # Stats caching
        self._stats_cache = None
        self._stats_cache_time = None
        self._stats_cache_duration = 60  # Cache for 60 seconds
        
        self.api_app = web.Application()
        # Add middleware for API key validation
        self.api_app.middlewares.append(api_key_middleware)

        # Existing bot endpoints (no API key required)
        self.api_app.router.add_get('/health', self.get_health)
        self.api_app.router.add_get('/shards', self.get_shards)
        self.api_app.router.add_get('/commands', self.get_commands_endpoint)
        self.api_app.router.add_get('/team', self.get_team)
        self.api_app.router.add_get('/templates', self.get_templates)
        self.api_app.router.add_get('/feedback', self.get_feedback)
        self.api_app.router.add_get('/avatars', self.get_avatars)
        self.api_app.router.add_get('/avatar', self.get_bot_avatar)
        self.api_app.router.add_get('/avatars/{user_id}', self.get_user_avatars)
        self.api_app.router.add_get('/history', self.get_history)
        self.api_app.router.add_get('/user/{user_id}', self.get_user)
        self.api_app.router.add_get('/featured-communities', self.get_featured_communities)
        self.api_app.router.add_get('/stats', self.get_stats)

        # Test endpoint to verify middleware is working
        self.api_app.router.add_get('/test-exempt', self.test_exempt_endpoint)
        self.api_app.router.add_get('/test-protected', self.test_protected_endpoint)

        # Fun endpoints (API key required)
        self.api_app.router.add_get('/fun/quran', self.get_quran)
        self.api_app.router.add_get('/fun/bible', self.get_bible)
        self.api_app.router.add_get('/fun/pack', self.get_pack)
        self.api_app.router.add_get('/fun/bird', self.get_bird)
        self.api_app.router.add_get('/fun/cat', self.get_cat)
        self.api_app.router.add_get('/fun/dog', self.get_dog)
        self.api_app.router.add_get('/fun/capybara', self.get_capybara)
        self.api_app.router.add_get('/fun/uselessfact', self.get_useless_fact)
        self.api_app.router.add_get('/fun/advice', self.get_advice)
        self.api_app.router.add_get('/fun/dadjoke', self.get_dad_joke)
        self.api_app.router.add_get('/fun/meme', self.get_meme)

        # Reaction GIF endpoints (API key required)
        self.api_app.router.add_get('/fun/lick', self.get_lick)
        self.api_app.router.add_get('/fun/kiss', self.get_kiss)
        self.api_app.router.add_get('/fun/pinch', self.get_pinch)
        self.api_app.router.add_get('/fun/cuddle', self.get_cuddle)
        self.api_app.router.add_get('/fun/hug', self.get_hug)
        self.api_app.router.add_get('/fun/pat', self.get_pat)
        self.api_app.router.add_get('/fun/slap', self.get_slap)
        self.api_app.router.add_get('/fun/laugh', self.get_laugh)
        self.api_app.router.add_get('/fun/cry', self.get_cry)
        self.api_app.router.add_get('/fun/kill', self.get_kill)
        self.api_app.router.add_get('/fun/stab', self.get_stab)
        self.api_app.router.add_get('/fun/eat', self.get_eat)
        self.api_app.router.add_get('/fun/kick', self.get_kick)
        self.api_app.router.add_get('/fun/goodmorning', self.get_goodmorning)
        self.api_app.router.add_get('/fun/goodnight', self.get_goodnight)
        self.api_app.router.add_get('/fun/bite', self.get_bite)
        self.api_app.router.add_get('/fun/poke', self.get_poke)
        self.api_app.router.add_get('/fun/tickle', self.get_tickle)
        self.api_app.router.add_get('/fun/dance', self.get_dance)
        self.api_app.router.add_get('/fun/wave', self.get_wave)
        self.api_app.router.add_get('/fun/highfive', self.get_highfive)

        # NSFW endpoints (API key required)
        self.api_app.router.add_get('/fun/molest', self.get_molest)
        self.api_app.router.add_get('/fun/fuck', self.get_fuck)
        self.api_app.router.add_get('/fun/anal', self.get_anal)
        self.api_app.router.add_get('/fun/blowjob', self.get_blowjob)
        self.api_app.router.add_get('/fun/cum', self.get_cum)
        self.api_app.router.add_get('/fun/pussylick', self.get_pussylick)
        self.api_app.router.add_get('/fun/threesome_fff', self.get_threesome_fff)
        self.api_app.router.add_get('/fun/threesome_ffm', self.get_threesome_ffm)
        self.api_app.router.add_get('/fun/threesome_fmm', self.get_threesome_fmm)
        self.api_app.router.add_get('/fun/tittysuck', self.get_tittysuck)
        self.api_app.router.add_get('/fun/hump', self.get_hump)
        self.api_app.router.add_get('/fun/footjob', self.get_footjob)

        # Bot endpoints (API key required)
        self.api_app.router.add_get('/guns/user', self.get_guns_user)
        self.api_app.router.add_get('/guns/uid', self.get_guns_uid)
        self.api_app.router.add_get('/uzi/user', self.get_uzi_user)
        self.api_app.router.add_get('/topgg/voted', self.get_topgg_vote)

        # Game endpoints (API key required)
        self.api_app.router.add_get('/roulette', self.get_roulette)

        # Utility endpoints (API key required)
        self.api_app.router.add_get('/weather', self.get_weather)
        self.api_app.router.add_get('/minecraft', self.get_minecraft_user)
        self.api_app.router.add_get('/discord/user', self.get_discord_user)
        self.api_app.router.add_get('/discord/guild', self.get_discord_guild)
        self.api_app.router.add_get('/discord/channel', self.get_discord_channel)
        self.api_app.router.add_get('/discord/invite', self.get_discord_invite)
        self.api_app.router.add_get('/github/user', self.get_github_user)
        self.api_app.router.add_get('/crypto', self.get_crypto)
        self.api_app.router.add_get('/stock', self.get_stock)
        self.api_app.router.add_get('/spotify/track', self.get_spotify_track)
        self.api_app.router.add_get('/google/reverse', self.get_google_reverse)
        self.api_app.router.add_get('/roblox/user', self.get_roblox_user)

        # Valorant endpoints (API key required)
        self.api_app.router.add_get('/valorant/user', self.get_valorant_user)
        self.api_app.router.add_get('/valorant/ranked/user', self.get_valorant_ranked_user)
        self.api_app.router.add_get('/valorant/ranked/matches', self.get_valorant_ranked_matches)

        # Social media endpoints (API key required)
        # Instagram
        self.api_app.router.add_get('/instagram/media', self.get_instagram_media)
        self.api_app.router.add_get('/instagram/post', self.get_instagram_post)
        self.api_app.router.add_get('/instagram/posts', self.get_instagram_posts)
        self.api_app.router.add_get('/instagram/story', self.get_instagram_story)
        self.api_app.router.add_get('/instagram/user', self.get_instagram_user)
        self.api_app.router.add_get('/instagram/timeline', self.get_instagram_timeline)
        self.api_app.router.add_get('/instagram/postinfo', self.get_instagram_postinfo)

        # TikTok
        self.api_app.router.add_get('/tiktok/media', self.get_tiktok_media)
        self.api_app.router.add_get('/tiktok/fyp', self.get_tiktok_fyp)
        self.api_app.router.add_get('/tiktok/user', self.get_tiktok_user)
        self.api_app.router.add_get('/tiktok/timeline', self.get_tiktok_timeline)

        # Twitter
        self.api_app.router.add_get('/twitter/media', self.get_twitter_media)
        self.api_app.router.add_get('/twitter/user', self.get_twitter_user)
        self.api_app.router.add_get('/twitter/timeline', self.get_twitter_timeline)

        # Snapchat
        self.api_app.router.add_get('/snapchat/media', self.get_snapchat_media)
        self.api_app.router.add_get('/snapchat/story', self.get_snapchat_story)
        self.api_app.router.add_get('/snapchat/user', self.get_snapchat_user)

        # OnlyFans
        self.api_app.router.add_get('/onlyfans/user', self.get_onlyfans_user)

        # Steam
        self.api_app.router.add_get('/steam/user', self.get_steam_user)

        # YouTube
        self.api_app.router.add_get('/youtube/channel', self.get_youtube_channel)
        self.api_app.router.add_get('/youtube/timeline', self.get_youtube_timeline)

        # Pinterest
        self.api_app.router.add_get('/pinterest/media', self.get_pinterest_media)

        self.api_runner = None
        
        self.static_app = web.Application()
        self.static_app.router.add_get('/{hash}', self.serve_static_file)
        self.static_app.router.add_get('/{hash}/', self.serve_static_file)
        self.static_app.router.add_get('/{hash}/index.html', self.serve_static_file)
        self.static_runner = None
        
        asyncio.create_task(self.start_webservers())

    async def serve_static_file(self, request):
        hash_value = request.match_info['hash']
        file_path = os.path.join('/var/www/html', hash_value)
        
        if os.path.isdir(file_path):
            file_path = os.path.join(file_path, 'index.html')
        
        if not os.path.abspath(file_path).startswith('/var/www/html/'):
            raise web.HTTPForbidden()
            
        if not os.path.exists(file_path):
            raise web.HTTPNotFound()
            
        return web.FileResponse(file_path)

    async def start_webservers(self):
        await self.bot.wait_until_ready()

        try:
            self.api_runner = web.AppRunner(self.api_app)
            await self.api_runner.setup()
            api_site = web.TCPSite(self.api_runner, '0.0.0.0', 80)
            await api_site.start()
            print(f"API server running on 0.0.0.0:80 (accessible externally)")
        except Exception as e:
            print(f"Failed to start API server: {e}")

        try:
            self.static_runner = web.AppRunner(self.static_app)
            await self.static_runner.setup()
            static_site = web.TCPSite(self.static_runner, '0.0.0.0', 8081)
            await static_site.start()
            print(f"Static file server running on 0.0.0.0:8081 (accessible externally)")
        except Exception as e:
            print(f"Failed to start static file server: {e}")

    async def get_health(self, request):
        """Health check endpoint that returns bot status and shard information"""
        try:
            # Get basic bot information
            uptime = self.bot.uptime
            if isinstance(uptime, datetime):
                uptime_seconds = (datetime.now() - uptime).total_seconds()
            elif hasattr(self.bot, 'start_time') and isinstance(self.bot.start_time, datetime):
                uptime_seconds = (datetime.now() - self.bot.start_time).total_seconds()
            else:
                uptime_seconds = 0

            # Calculate totals
            total_guilds = len(self.bot.guilds)
            total_users = sum(guild.member_count for guild in self.bot.guilds)

            # Build clusters data structure expected by botinfo/shards commands
            clusters = {}

            # Group shards by cluster (for now, we'll put all shards in cluster 0)
            cluster_id = "0"
            cluster_shards = []

            for shard_id, shard in self.bot.shards.items():
                # Calculate shard-specific stats
                shard_guilds = [g for g in self.bot.guilds if g.shard_id == shard_id]
                shard_guild_count = len(shard_guilds)
                shard_user_count = sum(guild.member_count for guild in shard_guilds)

                shard_info = {
                    'id': shard_id,
                    'latency': shard.latency if shard.latency else 0,
                    'guilds': shard_guild_count,
                    'users': shard_user_count,
                    'uptime': uptime_seconds if uptime_seconds else 0,
                    'seconds_since_seen': 0  # Always 0 since we're directly connected
                }
                cluster_shards.append(shard_info)

            # If no shards exist, create a default one
            if not cluster_shards:
                cluster_shards.append({
                    'id': 0,
                    'latency': self.bot.latency if self.bot.latency else 0,
                    'guilds': total_guilds,
                    'users': total_users,
                    'uptime': uptime_seconds if uptime_seconds else 0,
                    'seconds_since_seen': 0
                })

            clusters[cluster_id] = {
                'guilds': sum(shard['guilds'] for shard in cluster_shards),
                'users': sum(shard['users'] for shard in cluster_shards),
                'shards': cluster_shards
            }

            # Build the response in the format expected by botinfo/shards commands
            health_data = {
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'uptime': uptime_seconds,
                'users': total_users,
                'guilds': total_guilds,
                'clusters': clusters,
                'bot': {
                    'id': str(self.bot.user.id),
                    'name': self.bot.user.name,
                    'discriminator': self.bot.user.discriminator,
                    'avatar': self.bot.user.avatar.url if self.bot.user.avatar else None,
                    'latency': round(self.bot.latency * 1000, 2) if self.bot.latency else 0
                },
                'version': '1.0.0'
            }

            return web.json_response(health_data)
        except Exception as e:
            return web.json_response({
                'status': 'error',
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }, status=500)

    async def get_team(self, request):
        try:
            team = await self.bot.db.fetch("SELECT * FROM team_members")
            team_info = []
            for member in team:
                team_info.append({
                    'user_id': str(member['user_id']),
                    'rank': member['rank'],
                    'socials': member['socials']
                })
            return web.json_response(team_info)
        except Exception as e:
            return web.json_response({'error': str(e)}, status=500)

    async def get_templates(self, request):
        try:
            templates = await self.bot.db.fetch("SELECT * FROM embeds_templates ORDER BY id DESC")
            templates_info = []
            for template in templates:
                templates_info.append({
                    'id': str(template['id']),
                    'name': str(template['name']),
                    'user_id': str(template['user_id']),
                    'code': str(template['code']),
                    'embed': str(template['embed']),
                    'image': str(template['image']),
                })
            return web.json_response(templates_info)
        except Exception as e:
            return web.json_response({'error': str(e)}, status=500)

    async def get_feedback(self, request):
        try:
            feedback = await self.bot.db.fetch("SELECT * FROM testimonials")
            feedback_info = []
            for message in feedback:
                feedback_info.append({
                    'guild_id': str(message['guild_id']),
                    'user_id': str(message['user_id']),
                    'message_id': str(message['message_id']),
                    'feedback': str(message['feedback']),
                    'approved': message['approved'],
                })
            return web.json_response(feedback_info)
        except Exception as e:
            return web.json_response({'error': str(e)}, status=500)

    async def get_avatars(self, request):
        try:
            avatars = await self.bot.db.fetch("SELECT * FROM avatar_history")
            avatars_info = []
            for avatar in avatars:
                avatars_info.append({
                    'user_id': str(avatar['user_id']),
                    'avatar': str(avatar['avatar']),
                    'url': f"https://cdn.stun.lat/avatars/{avatar['avatar']}",
                    'timestamp': avatar['timestamp'],
                })
            return web.json_response(avatars_info)
        except Exception as e:
            return web.json_response({'error': str(e)}, status=500)

    async def get_featured_communities(self, request):
        """Get featured communities/guilds"""
        try:
            # Check if there's a featured_communities table in the database
            try:
                featured_guilds = await self.bot.db.fetch("SELECT * FROM featured_communities ORDER BY priority ASC")
            except:
                # If no table exists, use a hardcoded list of featured guild IDs
                # You can modify this list or create the table later
                featured_guild_ids = [
                    1362414311661633536
                    # Add your featured guild IDs here
                    # Example: 1234567890123456789, 9876543210987654321
                ]
                featured_guilds = [{'guild_id': guild_id, 'priority': i} for i, guild_id in enumerate(featured_guild_ids)]

            communities_info = []
            for guild_data in featured_guilds:
                guild_id = guild_data['guild_id']
                guild = self.bot.get_guild(guild_id)

                if guild:
                    # Get member count
                    member_count = guild.member_count

                    # Get online member count (approximate)
                    online_count = sum(1 for member in guild.members if member.status != discord.Status.offline) if guild.members else 0

                    # Get boost level and boost count
                    boost_level = guild.premium_tier
                    boost_count = guild.premium_subscription_count or 0

                    # Get verification level
                    verification_level = str(guild.verification_level)

                    # Get features
                    features = list(guild.features)

                    community_info = {
                        'id': str(guild.id),
                        'name': guild.name,
                        'description': guild.description,
                        'icon': guild.icon.url if guild.icon else None,
                        'banner': guild.banner.url if guild.banner else None,
                        'splash': guild.splash.url if guild.splash else None,
                        'member_count': member_count,
                        'online_count': online_count,
                        'boost_level': boost_level,
                        'boost_count': boost_count,
                        'verification_level': verification_level,
                        'features': features,
                        'created_at': guild.created_at.isoformat(),
                        'vanity_url': guild.vanity_url_code,
                        'priority': guild_data.get('priority', 0)
                    }

                    # Add invite link if available
                    try:
                        if guild.vanity_url_code:
                            community_info['invite_url'] = f"https://discord.gg/{guild.vanity_url_code}"
                        else:
                            # Try to get a general invite from the first available channel
                            for channel in guild.text_channels:
                                if channel.permissions_for(guild.me).create_instant_invite:
                                    invite = await channel.create_invite(max_age=0, max_uses=0, reason="Featured communities API")
                                    community_info['invite_url'] = invite.url
                                    break
                    except:
                        community_info['invite_url'] = None

                    communities_info.append(community_info)

            return web.json_response({
                'success': True,
                'count': len(communities_info),
                'communities': communities_info
            })

        except Exception as e:
            return web.json_response({'error': str(e)}, status=500)

    async def get_stats(self, request):
        """Comprehensive bot statistics endpoint"""
        try:
            # Check cache first
            now = datetime.now()
            if (self._stats_cache and self._stats_cache_time and
                (now - self._stats_cache_time).total_seconds() < self._stats_cache_duration):
                return web.json_response(self._stats_cache)

            # Initialize stats dictionary
            stats = {}

            # Code metrics
            try:
                code_lines, functions, classes = await self._get_code_metrics()
                stats['code_lines'] = code_lines
                stats['functions'] = functions
                stats['classes'] = classes
            except Exception:
                stats['code_lines'] = None
                stats['functions'] = None
                stats['classes'] = None

            # Database metrics
            try:
                db_rows, database_size = await self._get_database_metrics()
                stats['db_rows'] = db_rows
                stats['database_size'] = database_size
            except Exception:
                stats['db_rows'] = None
                stats['database_size'] = None

            # Bot uptime
            try:
                stats['uptime'] = self._get_uptime_string()
            except Exception:
                stats['uptime'] = None

            # Bot latency
            try:
                stats['latency'] = round(self.bot.latency * 1000) if self.bot.latency else None
            except Exception:
                stats['latency'] = None

            # Discord metrics
            try:
                stats['users'] = sum(guild.member_count for guild in self.bot.guilds)
                stats['servers'] = len(self.bot.guilds)
                stats['channels'] = sum(len(guild.channels) for guild in self.bot.guilds)
            except Exception:
                stats['users'] = None
                stats['servers'] = None
                stats['channels'] = None

            # Bot commands and cogs
            try:
                stats['commands'] = len(list(self.bot.walk_commands()))
                stats['cogs'] = len(self.bot.cogs)
            except Exception:
                stats['commands'] = None
                stats['cogs'] = None

            # System metrics
            try:
                process = psutil.Process()
                stats['memory_usage'] = round(process.memory_info().rss / 1024 / 1024, 1)  # MB
                stats['cpu_usage'] = round(process.cpu_percent(), 1)
            except Exception:
                stats['memory_usage'] = None
                stats['cpu_usage'] = None

            # Version information
            try:
                stats['python_version'] = platform.python_version()
                stats['discord_py_version'] = discord.__version__
            except Exception:
                stats['python_version'] = None
                stats['discord_py_version'] = None

            # Cache the results
            self._stats_cache = stats
            self._stats_cache_time = now

            return web.json_response(stats)

        except Exception as e:
            return web.json_response({'error': str(e)}, status=500)

    async def _get_code_metrics(self):
        """Count lines of code, functions, and classes across all Python files"""
        code_lines = 0
        functions = 0
        classes = 0

        # Get the bot directory (parent of cogs directory)
        bot_dir = os.path.dirname(os.path.dirname(__file__))

        for root, dirs, files in os.walk(bot_dir):
            # Skip certain directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]

            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            for line in f:
                                line = line.strip()
                                if line and not line.startswith('#'):
                                    code_lines += 1
                                if line.startswith('def '):
                                    functions += 1
                                elif line.startswith('class '):
                                    classes += 1
                    except Exception:
                        continue

        return code_lines, functions, classes

    async def _get_database_metrics(self):
        """Get database row count and size"""
        try:
            # Get all table names
            tables_query = """
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
            """
            tables = await self.bot.db.fetch(tables_query)

            total_rows = 0
            for table in tables:
                table_name = table['table_name']
                try:
                    count_result = await self.bot.db.fetchrow(f"SELECT COUNT(*) as count FROM {table_name}")
                    total_rows += count_result['count']
                except Exception:
                    continue

            # Get database size
            size_query = """
                SELECT pg_size_pretty(pg_database_size('stunbot')) as size
            """
            size_result = await self.bot.db.fetchrow(size_query)
            database_size = size_result['size'] if size_result else None

            return total_rows, database_size

        except Exception:
            return None, None

    def _get_uptime_string(self):
        """Get human-readable uptime string"""
        if hasattr(self.bot, 'start_time') and self.bot.start_time:
            uptime_delta = datetime.now() - self.bot.start_time
        elif hasattr(self.bot, 'uptime') and isinstance(self.bot.uptime, datetime):
            uptime_delta = datetime.now() - self.bot.uptime
        else:
            return None

        days = uptime_delta.days
        hours, remainder = divmod(uptime_delta.seconds, 3600)
        minutes, _ = divmod(remainder, 60)

        parts = []
        if days > 0:
            parts.append(f"{days} day{'s' if days != 1 else ''}")
        if hours > 0:
            parts.append(f"{hours} hour{'s' if hours != 1 else ''}")
        if minutes > 0:
            parts.append(f"{minutes} minute{'s' if minutes != 1 else ''}")

        return ", ".join(parts) if parts else "Less than a minute"

    async def test_exempt_endpoint(self, request):
        """Test endpoint that should NOT require an API key"""
        return web.json_response({
            'message': 'This endpoint is exempt from API key validation',
            'path': request.path,
            'status': 'success'
        })

    async def test_protected_endpoint(self, request):
        """Test endpoint that SHOULD require an API key"""
        return web.json_response({
            'message': 'This endpoint requires an API key',
            'path': request.path,
            'status': 'success'
        })

    async def get_user_avatars(self, request):
        try:
            user_id = int(request.match_info['user_id'])
            avatars = await self.bot.db.fetch("SELECT * FROM avatar_history WHERE user_id = $1", user_id)
            avatars_info = []
            for avatar in avatars:
                avatars_info.append({
                    'user_id': str(avatar['user_id']),
                    'avatar': str(avatar['avatar']),
                    'url': f"https://cdn.stun.lat/avatars/{avatar['avatar']}",
                    'timestamp': avatar['timestamp'],
                })
            return web.json_response(avatars_info)
        except Exception as e:
            return web.json_response({'error': str(e)}, status=500)

    async def get_bot_avatar(self, request):
        """Get the bot's avatar URL"""
        try:
            avatar_url = self.bot.user.avatar.url if self.bot.user.avatar else self.bot.user.default_avatar.url
            return web.json_response({'avatar': avatar_url})
        except Exception as e:
            return web.json_response({'error': str(e)}, status=500)

    async def get_history(self, request):
        try:
            history = await self.bot.db.fetch("SELECT * FROM growth")
            history_info = []
            for entry in history:
                history_info.append({
                    'guilds': entry['guilds'],
                    'users': entry['users'],
                    'ping': entry['ping'],
                    'timestamp': entry['timestamp'].isoformat() if entry['timestamp'] else None
                })
            return web.json_response(history_info)
        except Exception as e:
            return web.json_response({'error': str(e)}, status=500)

    async def get_user(self, request):
        try:
            user_id = int(request.match_info['user_id'])
            # Always fetch user to get complete profile data including banner
            user = await self.bot.fetch_user(user_id)
            if not user:
                return web.json_response({'error': 'User not found'}, status=404)

            # Initialize response data with basic user info
            user_data = {
                'id': str(user.id),
                'username': user.name,
                'global_name': user.global_name,
                'discriminator': user.discriminator,
                'bot': user.bot,
                'system': user.system,
                'created_at': user.created_at.isoformat(),
                'avatar': {
                    'url': user.avatar.url if user.avatar else user.default_avatar.url,
                    'hash': user.avatar.key if user.avatar else None,
                    'animated': user.avatar.is_animated() if user.avatar else False
                },
                'banner': {
                    'url': None,
                    'hash': None,
                    'color': None
                },
                'avatar_decoration': {
                    'url': None,
                    'hash': None
                },
                'accent_color': None,
                'public_flags': [],
                'premium_type': None,
                'status': 'offline',
                'custom_status': None,
                'activities': [],
                'presence': {
                    'status': 'offline',
                    'desktop_status': None,
                    'mobile_status': None,
                    'web_status': None
                }
            }

            # Handle banner information
            user_data['banner']['url'] = user.banner.url if user.banner else None
            if user.banner:
                user_data['banner']['hash'] = user.banner.key
            if hasattr(user, 'banner_color') and user.banner_color:
                user_data['banner']['color'] = str(user.banner_color)

            # Handle avatar decoration
            if user.avatar_decoration:
                user_data['avatar_decoration']['url'] = user.avatar_decoration.url
                user_data['avatar_decoration']['hash'] = user.avatar_decoration.key

            # Handle accent color
            if hasattr(user, 'accent_color') and user.accent_color:
                user_data['accent_color'] = str(user.accent_color)
                # Also set banner color if no specific banner color is set
                if not user_data['banner']['color']:
                    user_data['banner']['color'] = str(user.accent_color)

            # Handle public flags (badges)
            try:
                if hasattr(user, 'public_flags') and user.public_flags:
                    user_data['public_flags'] = [flag.name for flag in user.public_flags if hasattr(flag, 'name')]
            except Exception:
                user_data['public_flags'] = []

            # Try to get member data from mutual guilds for presence/activity info
            member = None
            guild = self.bot.get_guild(self.bot.logging_guild)
            if guild:
                member = guild.get_member(user.id)

            # If not found in logging guild, try other mutual guilds
            if not member:
                for guild in self.bot.guilds:
                    member = guild.get_member(user.id)
                    if member:
                        break

            if member:
                # Status information
                user_data['status'] = str(member.status)
                user_data['presence']['status'] = str(member.status)

                # Platform-specific status
                if hasattr(member, 'desktop_status'):
                    user_data['presence']['desktop_status'] = str(member.desktop_status) if member.desktop_status else None
                if hasattr(member, 'mobile_status'):
                    user_data['presence']['mobile_status'] = str(member.mobile_status) if member.mobile_status else None
                if hasattr(member, 'web_status'):
                    user_data['presence']['web_status'] = str(member.web_status) if member.web_status else None

                # Activities
                activities = []
                if member.activities:
                    for activity in member.activities:
                        try:
                            activity_data = {
                                'name': getattr(activity, 'name', 'Unknown'),
                                'type': getattr(activity.type, 'value', 0) if hasattr(activity, 'type') else 0,
                                'type_name': getattr(activity.type, 'name', 'unknown').lower() if hasattr(activity, 'type') else 'unknown',
                                'url': getattr(activity, 'url', None),
                                'details': getattr(activity, 'details', None),
                                'state': getattr(activity, 'state', None),
                                'timestamps': None,
                                'emoji': None,
                                'party': None,
                                'assets': None,
                                'secrets': None,
                                'instance': getattr(activity, 'instance', None),
                                'flags': getattr(activity, 'flags', None)
                            }
                        except Exception:
                            # Skip this activity if there's an error
                            continue

                        try:
                            # Handle timestamps
                            if hasattr(activity, 'start') or hasattr(activity, 'end'):
                                activity_data['timestamps'] = {
                                    'start': activity.start.timestamp() if hasattr(activity, 'start') and activity.start else None,
                                    'end': activity.end.timestamp() if hasattr(activity, 'end') and activity.end else None
                                }
                        except Exception:
                            pass

                        try:
                            # Handle emoji (for custom status)
                            if hasattr(activity, 'emoji') and activity.emoji:
                                activity_data['emoji'] = {
                                    'name': getattr(activity.emoji, 'name', None),
                                    'id': str(activity.emoji.id) if getattr(activity.emoji, 'id', None) else None,
                                    'animated': getattr(activity.emoji, 'animated', False),
                                    'url': getattr(activity.emoji, 'url', None) if hasattr(activity.emoji, 'url') else None
                                }
                        except Exception:
                            pass

                        try:
                            # Handle party information
                            if hasattr(activity, 'party') and activity.party:
                                activity_data['party'] = {
                                    'id': getattr(activity.party, 'id', None),
                                    'size': getattr(activity.party, 'size', None)
                                }
                        except Exception:
                            pass

                        try:
                            # Handle assets (images)
                            if hasattr(activity, 'large_image_url') or hasattr(activity, 'small_image_url'):
                                activity_data['assets'] = {
                                    'large_image': getattr(activity, 'large_image_url', None),
                                    'large_text': getattr(activity, 'large_image_text', None),
                                    'small_image': getattr(activity, 'small_image_url', None),
                                    'small_text': getattr(activity, 'small_image_text', None)
                                }
                        except Exception:
                            pass

                        try:
                            # Special handling for Spotify
                            if hasattr(activity, '__class__') and activity.__class__.__name__ == 'Spotify':
                                duration = getattr(activity, 'duration', None)
                                # Convert timedelta to seconds if it exists
                                if duration and hasattr(duration, 'total_seconds'):
                                    duration = duration.total_seconds()

                                activity_data.update({
                                    'title': getattr(activity, 'title', None),
                                    'artist': getattr(activity, 'artist', None),
                                    'album': getattr(activity, 'album', None),
                                    'album_cover_url': getattr(activity, 'album_cover_url', None),
                                    'track_id': getattr(activity, 'track_id', None),
                                    'duration': duration
                                })
                        except Exception:
                            pass

                        try:
                            # Custom status handling
                            if hasattr(activity, 'type') and hasattr(activity.type, 'value') and activity.type.value == 4:  # Custom status
                                user_data['custom_status'] = {
                                    'text': getattr(activity, 'name', None),
                                    'emoji': activity_data.get('emoji')
                                }
                        except Exception:
                            pass

                        activities.append(activity_data)

                user_data['activities'] = activities

            return web.json_response(user_data)
        except Exception as e:
            return web.json_response({'error': str(e)}, status=500)

    def parse_duration(self, duration_str):
        duration_pattern = r'(?P<value>\d+)\s*(?P<unit>seconds?|minutes?|hours?|days?)'
        match = re.match(duration_pattern, duration_str)
        if not match:
            raise ValueError(f"Unsupported duration format: {duration_str}")
        value = int(match.group('value'))
        unit = match.group('unit').lower()
        if 'second' in unit:
            return timedelta(seconds=value)
        elif 'minute' in unit:
            return timedelta(minutes=value)
        elif 'hour' in unit:
            return timedelta(hours=value)
        elif 'day' in unit:
            return timedelta(days=value)
        else:
            raise ValueError(f"Unsupported time unit: {unit}")

    def get_shards_data(self):
        shard_data = []
        for shard_id, shard in self.bot.shards.items():
            latency_ms = float('inf') if shard.latency == float('inf') else round(shard.latency * 1000)
            uptime = self.bot.uptime
            if isinstance(uptime, str):
                try:
                    uptime_duration = self.parse_duration(uptime)
                    uptime = datetime.now() - uptime_duration
                except ValueError:
                    uptime = None
            shard_info = {
                'shard_id': shard_id,
                'is_ready': not shard.is_closed(),
                'server_count': sum(1 for guild in self.bot.guilds if guild.shard_id == shard_id),
                'member_count': sum(guild.member_count for guild in self.bot.guilds if guild.shard_id == shard_id),
                'uptime': (datetime.now() - uptime).total_seconds() if uptime else None,
                'latency': latency_ms,
                'last_updated': datetime.now().isoformat()
            }
            shard_data.append(shard_info)
        return shard_data

    async def get_shards(self, request):
        return web.json_response(self.get_shards_data())

    async def get_commands_endpoint(self, request):
        commands_info = get_commands_info(self.bot.commands)
        return web.json_response(commands_info)

    # Fun endpoints
    async def get_quran(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.alquran.cloud/v1/quran/en.asad') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a quran verse.'}, status=500)

    async def get_bible(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://beta.ourmanna.com/api/v1/get?format=json&order=random') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a bible verse.'}, status=500)

    async def get_pack(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://evilinsult.com/generate_insult.php?lang=en&type=json') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a pack image.'}, status=500)

    async def get_bird(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.alexflipnote.dev/birb') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a bird image.'}, status=500)

    async def get_cat(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.thecatapi.com/v1/images/search') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a cat image.'}, status=500)

    async def get_dog(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://random.dog/woof.json') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a dog image.'}, status=500)

    async def get_capybara(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.capy.lol/v1/capybara?json=true') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a capybara image.'}, status=500)

    async def get_useless_fact(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://uselessfacts.jsph.pl/random.json?language=en') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a useless fact.'}, status=500)

    async def get_advice(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.adviceslip.com/advice') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching advice.'}, status=500)

    async def get_dad_joke(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://icanhazdadjoke.com/slack') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a dad joke.'}, status=500)

    async def get_meme(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://meme-api.com/gimme') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a meme.'}, status=500)

    # Bot endpoints
    async def get_guns_user(self, request):
        username = request.query.get('username')
        if not username:
            return web.json_response({'error': '400', 'message': 'Parameter "username" is required'}, status=400)

        try:
            # Try username lookup first
            async with aiohttp.ClientSession() as session:
                payload = {'key': 'YOUR_GUNS_API_KEY', 'username': username}
                async with session.post('https://guns.lol/api/user/lookup?type=username', json=payload) as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        return web.json_response(data)
                    else:
                        # Try alias lookup
                        payload = {'key': 'YOUR_GUNS_API_KEY', 'alias': username}
                        async with session.post('https://guns.lol/api/user/lookup?type=alias', json=payload) as resp2:
                            if resp2.status == 200:
                                data = await resp2.json()
                                return web.json_response(data)
                            else:
                                return web.json_response({'error': '500', 'message': 'Error fetching informations from the Guns.lol Username or Alias.'}, status=500)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching informations from the Guns.lol Username or Alias.'}, status=500)

    async def get_guns_uid(self, request):
        uid = request.query.get('id')
        if not uid:
            return web.json_response({'error': '400', 'message': 'Parameter "id" is required'}, status=400)

        try:
            uid_int = int(uid)
        except ValueError:
            return web.json_response({'error': '400', 'message': '"id" must be a valid integer'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                payload = {'key': 'YOUR_GUNS_API_KEY', 'uid': uid_int}
                async with session.post('https://guns.lol/api/user/lookup?type=uid', json=payload) as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching informations from the Guns.lol UID.'}, status=500)

    async def get_uzi_user(self, request):
        username = request.query.get('username')
        if not username:
            return web.json_response({'error': '400', 'message': 'Parameter "username" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                payload = {'key': 'YOUR_UZI_API_KEY', 'username': username}
                async with session.post('https://uzi.bio/api/bot/lookup2', json=payload) as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching informations from the Uzi.bio username.'}, status=500)

    async def get_topgg_vote(self, request):
        user_id = request.query.get('id')
        if not user_id:
            return web.json_response({'error': '400', 'message': 'Parameter "id" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                headers = {'Authorization': 'YOUR_TOPGG_API_KEY'}
                async with session.get(f'https://top.gg/api/bots/1242930981967757452/check?userId={user_id}', headers=headers) as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching information from Top.gg API.'}, status=500)

    # Game endpoints
    async def get_roulette(self, request):
        bet = request.query.get('bet')
        amount = request.query.get('amount')

        if not bet:
            return web.json_response({'error': '400', 'message': 'Parameter "bet" is required'}, status=400)
        if not amount:
            return web.json_response({'error': '400', 'message': 'Parameter "amount" is required'}, status=400)

        try:
            amount_float = float(amount)
            result = self._play_roulette(bet, amount_float)
            return web.json_response(result)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error processing the Roulette bet'}, status=500)

    def _play_roulette(self, bet_str, wager):
        """Roulette game logic"""
        # Constants
        BET_NUMBER_MIN, BET_NUMBER_MAX = 0, 36
        NUMBERS_RED = [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36]
        NUMBERS_BLACK = [2, 4, 6, 8, 10, 11, 13, 15, 17, 20, 22, 24, 26, 28, 29, 31, 33, 35]
        NUMBERS_GREEN = [0]
        PAYOUT_MULTIPLIERS = {'number': 36, 'color_rb': 2, 'parity': 2, 'color_green': 36, 'dozen': 3}

        # Generate roll
        roll_number = random.randint(0, 36)

        # Determine roll properties
        if roll_number in NUMBERS_RED:
            roll_color = 'red'
        elif roll_number in NUMBERS_BLACK:
            roll_color = 'black'
        else:
            roll_color = 'green'

        roll_parity = 'even' if roll_number % 2 == 0 else 'odd'

        if 1 <= roll_number <= 12:
            roll_dozen = 'low'
        elif 13 <= roll_number <= 24:
            roll_dozen = 'middle'
        elif 25 <= roll_number <= 36:
            roll_dozen = 'high'
        else:
            roll_dozen = None

        # Validate bet and determine type
        bet_lower = bet_str.lower()
        if bet_lower.isdigit():
            bet_num = int(bet_lower)
            if BET_NUMBER_MIN <= bet_num <= BET_NUMBER_MAX:
                bet_type = 'number'
                did_win = roll_number == bet_num
            else:
                bet_type = 'invalid'
                did_win = False
        elif bet_lower in ['red', 'black']:
            bet_type = 'color_rb'
            did_win = roll_color == bet_lower
        elif bet_lower == 'green':
            bet_type = 'color_green'
            did_win = roll_color == 'green'
        elif bet_lower in ['even', 'odd']:
            bet_type = 'parity'
            did_win = roll_parity == bet_lower
        elif bet_lower in ['low', 'middle', 'high']:
            bet_type = 'dozen'
            did_win = roll_dozen == bet_lower
        else:
            bet_type = 'invalid'
            did_win = False

        # Calculate payout
        payout_rate = PAYOUT_MULTIPLIERS.get(bet_type, 0) if did_win else 0
        payout_amount = wager * payout_rate

        return {
            'success': True,
            'roll': {
                'number': roll_number,
                'color': roll_color,
                'parity': roll_parity,
                'dozen': roll_dozen,
            },
            'bet': {
                'bet': bet_str,
                'wager': f"{wager:.2f}",
                'win': did_win,
                'payout_rate': payout_rate,
                'payout': f"{payout_amount:.2f}",
            },
        }

    # Utility endpoints
    async def get_weather(self, request):
        location = request.query.get('location')
        if not location:
            return web.json_response({'error': '400', 'message': 'Parameter "location" is required'}, status=400)

        try:
            # This would need a weather API key
            return web.json_response({'error': '500', 'message': 'Weather API not configured'}, status=500)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching weather data'}, status=500)

    async def get_minecraft_user(self, request):
        username = request.query.get('username')
        if not username:
            return web.json_response({'error': '400', 'message': 'Parameter "username" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                # Get UUID from Mojang
                async with session.get(f'https://api.mojang.com/users/profiles/minecraft/{username}') as resp:
                    if resp.status != 200:
                        return web.json_response({'error': '404', 'message': 'Minecraft user not found'}, status=404)

                    mojang_data = await resp.json()
                    uuid = mojang_data['id']
                    name = mojang_data['name']

                # Get additional data from Laby
                async with session.get(f'https://laby.net/api/v3/user/{uuid}/profile') as resp2:
                    if resp2.status == 200:
                        laby_data = await resp2.json()
                        name_history = laby_data.get('name_history', [])
                        history = [{'name': entry['name'], 'changed': entry.get('changed_at')} for entry in name_history]
                    else:
                        history = []

                head_url = f"https://mineskin.eu/avatar/{username}"
                skin_url = f"https://visage.surgeplay.com/full/{uuid}"

                return web.json_response({
                    'uuid': uuid,
                    'name': name,
                    'history': history,
                    'head': head_url,
                    'skin': skin_url
                })
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Minecraft user profile.'}, status=500)

    async def get_discord_user(self, request):
        user_id = request.query.get('id')
        if not user_id:
            return web.json_response({'error': '400', 'message': 'Parameter "id" is required'}, status=400)

        try:
            # Use the bot's own Discord API access
            user = self.bot.get_user(int(user_id))
            if not user:
                user = await self.bot.fetch_user(int(user_id))

            if not user:
                return web.json_response({'error': '404', 'message': 'Discord user not found'}, status=404)

            return web.json_response({
                'id': str(user.id),
                'username': user.name,
                'discriminator': user.discriminator,
                'avatar': user.avatar.url if user.avatar else user.default_avatar.url,
                'bot': user.bot,
                'created_at': user.created_at.isoformat()
            })
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Discord user information.'}, status=500)

    async def get_github_user(self, request):
        username = request.query.get('username')
        if not username:
            return web.json_response({'error': '400', 'message': 'Parameter "username" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f'https://api.github.com/users/{username}') as resp:
                    if resp.status == 404:
                        return web.json_response({'error': '404', 'message': 'GitHub user not found'}, status=404)
                    elif resp.status != 200:
                        return web.json_response({'error': '500', 'message': 'Error fetching GitHub user'}, status=500)

                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching GitHub user information.'}, status=500)

    # Additional utility endpoints
    async def get_crypto(self, request):
        from_currency = request.query.get('from')
        to_currency = request.query.get('to')
        amount = request.query.get('amount', '1')

        if not from_currency or not to_currency:
            return web.json_response({'error': '400', 'message': 'Parameters "from" and "to" are required'}, status=400)

        try:
            # This would need a crypto API key
            return web.json_response({'error': '500', 'message': 'Crypto API not configured'}, status=500)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching crypto data'}, status=500)

    async def get_stock(self, request):
        symbol = request.query.get('symbol')
        if not symbol:
            return web.json_response({'error': '400', 'message': 'Parameter "symbol" is required'}, status=400)

        try:
            # This would need a stock API key
            return web.json_response({'error': '500', 'message': 'Stock API not configured'}, status=500)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching stock data'}, status=500)

    async def get_spotify_track(self, request):
        track_id = request.query.get('id')
        if not track_id:
            return web.json_response({'error': '400', 'message': 'Parameter "id" is required'}, status=400)

        try:
            # This would need Spotify API credentials
            return web.json_response({'error': '500', 'message': 'Spotify API not configured'}, status=500)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Spotify track'}, status=500)

    # Instagram endpoints
    async def get_instagram_media(self, request):
        url = request.query.get('url')
        if not url:
            return web.json_response({'error': '400', 'message': 'Parameter "url" is required'}, status=400)

        try:
            # Replace reels with reel for compatibility
            modified_url = url.replace('/reels/', '/reel/')

            async with aiohttp.ClientSession() as session:
                headers = {'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY'}
                async with session.get(f'https://social-api4.p.rapidapi.com/v1/post_info?code_or_id_or_url={modified_url}', headers=headers) as resp:
                    if resp.status != 200:
                        return web.json_response({'error': '500', 'message': 'Error fetching Instagram media'}, status=500)

                    data = await resp.json()
                    post_data = data.get('data', {})

                    extracted_data = {
                        'author': {
                            'id': post_data.get('user', {}).get('id'),
                            'username': post_data.get('user', {}).get('username'),
                            'display': post_data.get('user', {}).get('full_name'),
                            'avatar': post_data.get('user', {}).get('profile_pic_url'),
                        },
                        'video': {
                            'video': post_data.get('video_url'),
                            'caption': post_data.get('caption', {}).get('text'),
                            'likes': post_data.get('metrics', {}).get('like_count', 0),
                            'comments': post_data.get('metrics', {}).get('comment_count', 0),
                            'shares': post_data.get('metrics', {}).get('share_count', 0),
                            'views': post_data.get('metrics', {}).get('play_count', 0),
                        },
                    }
                    return web.json_response(extracted_data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Instagram media'}, status=500)

    async def get_instagram_post(self, request):
        url = request.query.get('url')
        if not url:
            return web.json_response({'error': '400', 'message': 'Parameter "url" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                headers = {'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY'}
                async with session.get(f'https://social-api4.p.rapidapi.com/v1/post_info?code_or_id_or_url={url}', headers=headers) as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Instagram post'}, status=500)

    async def get_instagram_posts(self, request):
        username = request.query.get('username')
        if not username:
            return web.json_response({'error': '400', 'message': 'Parameter "username" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                headers = {'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY'}
                async with session.get(f'https://social-api4.p.rapidapi.com/v1.2/posts?username_or_id_or_url={username}', headers=headers) as resp:
                    data = await resp.json()
                    # Limit to 50 items as in JS version
                    if 'data' in data and 'items' in data['data']:
                        data['data']['items'] = data['data']['items'][:50]
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Instagram posts'}, status=500)

    async def get_instagram_story(self, request):
        username = request.query.get('username')
        if not username:
            return web.json_response({'error': '400', 'message': 'Parameter "username" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                headers = {'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY'}
                async with session.get(f'https://social-api4.p.rapidapi.com/v1/stories?username_or_id_or_url={username}', headers=headers) as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Instagram story'}, status=500)

    async def get_instagram_user(self, request):
        username = request.query.get('username')
        if not username:
            return web.json_response({'error': '400', 'message': 'Parameter "username" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                headers = {'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY'}
                async with session.get(f'https://social-api4.p.rapidapi.com/v1/info?username_or_id_or_url={username}', headers=headers) as resp:
                    if resp.status != 200:
                        return web.json_response({'error': '500', 'message': 'Error fetching Instagram user'}, status=500)

                    data = await resp.json()
                    user_data = data.get('data', {})

                    extracted_data = {
                        'username': user_data.get('username'),
                        'full_name': user_data.get('full_name'),
                        'bio': user_data.get('biography'),
                        'profile_pic': user_data.get('profile_pic_url_hd'),
                        'followers': user_data.get('follower_count', 0),
                        'following': user_data.get('following_count', 0),
                        'posts': user_data.get('media_count', 0),
                        'is_verified': user_data.get('is_verified', False),
                        'is_private': user_data.get('is_private', False),
                    }
                    return web.json_response(extracted_data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Instagram user'}, status=500)

    async def get_instagram_timeline(self, request):
        username = request.query.get('username')
        if not username:
            return web.json_response({'error': '400', 'message': 'Parameter "username" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                headers = {'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY'}
                async with session.get(f'https://social-api4.p.rapidapi.com/v1/posts?username_or_id_or_url={username}&url_embed_safe=true', headers=headers) as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Instagram timeline'}, status=500)

    async def get_instagram_postinfo(self, request):
        code = request.query.get('code')
        if not code:
            return web.json_response({'error': '400', 'message': 'Parameter "code" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                headers = {'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY'}
                async with session.get(f'https://social-api4.p.rapidapi.com/v1/post_info?code_or_id_or_url={code}&include_insights=true', headers=headers) as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Instagram post info'}, status=500)

    # TikTok endpoints
    async def get_tiktok_media(self, request):
        url = request.query.get('url')
        if not url:
            return web.json_response({'error': '400', 'message': 'Parameter "url" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                headers = {'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY'}
                async with session.get(f'https://tiktok-best-experience.p.rapidapi.com/video/{url}', headers=headers) as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching TikTok media'}, status=500)

    async def get_tiktok_fyp(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                headers = {'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY'}
                async with session.get('https://tiktok-best-experience.p.rapidapi.com/feed/for_you', headers=headers) as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching TikTok FYP'}, status=500)

    async def get_tiktok_user(self, request):
        username = request.query.get('username')
        if not username:
            return web.json_response({'error': '400', 'message': 'Parameter "username" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                headers = {'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY'}
                async with session.get(f'https://tiktok-best-experience.p.rapidapi.com/user/{username}', headers=headers) as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching TikTok user'}, status=500)

    async def get_tiktok_timeline(self, request):
        username = request.query.get('username')
        if not username:
            return web.json_response({'error': '400', 'message': 'Parameter "username" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                headers = {'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY'}
                async with session.get(f'https://tiktok-best-experience.p.rapidapi.com/user/{username}/feed', headers=headers) as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching TikTok timeline'}, status=500)

    # Twitter endpoints
    async def get_twitter_media(self, request):
        url = request.query.get('url')
        if not url:
            return web.json_response({'error': '400', 'message': 'Parameter "url" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                headers = {'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY'}
                async with session.get(f'https://twitter-api45.p.rapidapi.com/tweet.php?url={url}', headers=headers) as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Twitter media'}, status=500)

    async def get_twitter_user(self, request):
        username = request.query.get('username')
        if not username:
            return web.json_response({'error': '400', 'message': 'Parameter "username" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                headers = {'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY'}
                async with session.get(f'https://twitter-api45.p.rapidapi.com/screenname.php?screenname={username}', headers=headers) as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Twitter user'}, status=500)

    async def get_twitter_timeline(self, request):
        username = request.query.get('username')
        if not username:
            return web.json_response({'error': '400', 'message': 'Parameter "username" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                headers = {'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY'}
                async with session.get(f'https://twitter-api45.p.rapidapi.com/timeline.php?screenname={username}', headers=headers) as resp:
                    data = await resp.json()
                    if data.get('status') == 'error':
                        return web.json_response({'error': '500', 'message': 'Error fetching Twitter timeline'}, status=500)
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Twitter timeline'}, status=500)

    # Snapchat endpoints
    async def get_snapchat_media(self, request):
        url = request.query.get('url')
        if not url:
            return web.json_response({'error': '400', 'message': 'Parameter "url" is required'}, status=400)

        try:
            # This would need specific Snapchat media extraction logic
            return web.json_response({'error': '500', 'message': 'Snapchat media extraction not implemented'}, status=500)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Snapchat media'}, status=500)

    async def get_snapchat_story(self, request):
        username = request.query.get('username')
        if not username:
            return web.json_response({'error': '400', 'message': 'Parameter "username" is required'}, status=400)

        try:
            # This would need specific Snapchat story extraction logic
            return web.json_response({'error': '500', 'message': 'Snapchat story extraction not implemented'}, status=500)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Snapchat story'}, status=500)

    async def get_snapchat_user(self, request):
        username = request.query.get('username')
        if not username:
            return web.json_response({'error': '400', 'message': 'Parameter "username" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}
                async with session.get(f'https://story.snapchat.com/add/{username}', headers=headers) as resp:
                    if resp.status != 200:
                        return web.json_response({'error': '404', 'message': 'Account not found'}, status=404)

                    html = await resp.text()
                    # This would need HTML parsing logic similar to the JS version
                    return web.json_response({'error': '500', 'message': 'Snapchat user parsing not fully implemented'}, status=500)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Snapchat user'}, status=500)

    # OnlyFans endpoints
    async def get_onlyfans_user(self, request):
        username = request.query.get('username')
        if not username:
            return web.json_response({'error': '400', 'message': 'Parameter "username" is required'}, status=400)

        try:
            # This would need OnlyFans API access
            return web.json_response({'error': '500', 'message': 'OnlyFans API not implemented'}, status=500)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching OnlyFans user'}, status=500)

    # Steam endpoints
    async def get_steam_user(self, request):
        steam_id = request.query.get('id')
        if not steam_id:
            return web.json_response({'error': '400', 'message': 'Parameter "id" is required'}, status=400)

        try:
            # This would need Steam API key
            return web.json_response({'error': '500', 'message': 'Steam API not configured'}, status=500)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Steam user'}, status=500)

    # YouTube endpoints
    async def get_youtube_channel(self, request):
        channel = request.query.get('channel')
        if not channel:
            return web.json_response({'error': '400', 'message': 'Parameter "channel" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                headers = {'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY'}
                async with session.get(f'https://youtube-v2.p.rapidapi.com/channel/id?channel_name={channel}', headers=headers) as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching YouTube channel'}, status=500)

    async def get_youtube_timeline(self, request):
        channel_id = request.query.get('channel_id')
        if not channel_id:
            return web.json_response({'error': '400', 'message': 'Parameter "channel_id" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                headers = {'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY'}
                async with session.get(f'https://youtube-v2.p.rapidapi.com/channel/videos?channel_id={channel_id}', headers=headers) as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching YouTube timeline'}, status=500)

    # Pinterest endpoints
    async def get_pinterest_media(self, request):
        url = request.query.get('url')
        if not url:
            return web.json_response({'error': '400', 'message': 'Parameter "url" is required'}, status=400)

        try:
            async with aiohttp.ClientSession() as session:
                headers = {'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY'}
                async with session.get(f'https://pinterest-video-and-image-downloader.p.rapidapi.com/pinterest?url={url}', headers=headers) as resp:
                    data = await resp.json()
                    if data.get('success') == False:
                        return web.json_response({'error': '500', 'message': 'Error fetching Pinterest media'}, status=500)
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Pinterest media'}, status=500)

    # Reaction GIF endpoints
    async def get_lick(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=lick&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a lick image.'}, status=500)

    async def get_kiss(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=kiss&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a kiss image.'}, status=500)

    async def get_pinch(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=pinch&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a pinch image.'}, status=500)

    async def get_cuddle(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=cuddle&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a cuddle image.'}, status=500)

    async def get_hug(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=hug&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a hug image.'}, status=500)

    async def get_pat(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=pat&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a pat image.'}, status=500)

    async def get_slap(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=slap&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a slap image.'}, status=500)

    async def get_laugh(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=laugh&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a laugh image.'}, status=500)

    async def get_cry(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=cry&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a cry image.'}, status=500)

    # Additional SFW Reaction GIF endpoints
    async def get_kill(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=kill&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a kill image.'}, status=500)

    async def get_stab(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=stab&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a stab image.'}, status=500)

    async def get_eat(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=eat&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching an eat image.'}, status=500)

    async def get_kick(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=kick&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a kick image.'}, status=500)

    async def get_goodmorning(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=goodmorning&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a good morning image.'}, status=500)

    async def get_goodnight(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=goodnight&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a good night image.'}, status=500)

    async def get_bite(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=bite&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a bite image.'}, status=500)

    async def get_poke(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=poke&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a poke image.'}, status=500)

    async def get_tickle(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=tickle&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a tickle image.'}, status=500)

    async def get_dance(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=dance&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a dance image.'}, status=500)

    async def get_wave(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=wave&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a wave image.'}, status=500)

    async def get_highfive(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.otakugifs.xyz/gif?reaction=highfive&format=gif') as resp:
                    data = await resp.json()
                    return web.json_response(data)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a high five image.'}, status=500)

    # NSFW endpoints
    async def get_molest(self, request):
        random_gif_number = random.randint(1, 20)
        return web.json_response({'url': f'https://api.stun.lat/fun/molest/{random_gif_number}.gif'})

    async def get_fuck(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://purrbot.site/api/img/nsfw/fuck/gif') as resp:
                    data = await resp.json()
                    return web.json_response({'url': data.get('link')})
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a fuck image.'}, status=500)

    async def get_anal(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://purrbot.site/api/img/nsfw/anal/gif') as resp:
                    data = await resp.json()
                    return web.json_response({'url': data.get('link')})
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching an anal image.'}, status=500)

    async def get_blowjob(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://purrbot.site/api/img/nsfw/blowjob/gif') as resp:
                    data = await resp.json()
                    return web.json_response({'url': data.get('link')})
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a blowjob image.'}, status=500)

    async def get_cum(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://purrbot.site/api/img/nsfw/cum/gif') as resp:
                    data = await resp.json()
                    return web.json_response({'url': data.get('link')})
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a cum image.'}, status=500)

    async def get_pussylick(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://purrbot.site/api/img/nsfw/pussylick/gif') as resp:
                    data = await resp.json()
                    return web.json_response({'url': data.get('link')})
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a pussylick image.'}, status=500)

    async def get_threesome_fff(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://purrbot.site/api/img/nsfw/threesome_fff/gif') as resp:
                    data = await resp.json()
                    return web.json_response({'url': data.get('link')})
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a threesome FFF image.'}, status=500)

    async def get_threesome_ffm(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://purrbot.site/api/img/nsfw/threesome_ffm/gif') as resp:
                    data = await resp.json()
                    return web.json_response({'url': data.get('link')})
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a threesome FFM image.'}, status=500)

    async def get_threesome_fmm(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://purrbot.site/api/img/nsfw/threesome_mmf/gif') as resp:
                    data = await resp.json()
                    return web.json_response({'url': data.get('link')})
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a threesome FMM image.'}, status=500)

    async def get_tittysuck(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://purrbot.site/api/img/nsfw/suck/gif') as resp:
                    data = await resp.json()
                    return web.json_response({'url': data.get('link')})
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a tittysuck image.'}, status=500)

    async def get_hump(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://purrbot.site/api/img/nsfw/hump/gif') as resp:
                    data = await resp.json()
                    return web.json_response({'url': data.get('link')})
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a hump image.'}, status=500)

    async def get_footjob(self, request):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://purrbot.site/api/img/nsfw/footjob/gif') as resp:
                    data = await resp.json()
                    return web.json_response({'url': data.get('link')})
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching a footjob image.'}, status=500)

    # Additional Discord endpoints
    async def get_discord_guild(self, request):
        guild_id = request.query.get('id')
        if not guild_id:
            return web.json_response({'error': '400', 'message': 'Parameter "id" is required'}, status=400)

        try:
            guild = self.bot.get_guild(int(guild_id))
            if not guild:
                return web.json_response({'error': '404', 'message': 'Guild not found'}, status=404)

            return web.json_response({
                'id': str(guild.id),
                'name': guild.name,
                'description': guild.description,
                'icon': guild.icon.url if guild.icon else None,
                'banner': guild.banner.url if guild.banner else None,
                'member_count': guild.member_count,
                'created_at': guild.created_at.isoformat()
            })
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Discord guild information.'}, status=500)

    async def get_discord_channel(self, request):
        channel_id = request.query.get('id')
        if not channel_id:
            return web.json_response({'error': '400', 'message': 'Parameter "id" is required'}, status=400)

        try:
            channel = self.bot.get_channel(int(channel_id))
            if not channel:
                return web.json_response({'error': '404', 'message': 'Channel not found'}, status=404)

            return web.json_response({
                'id': str(channel.id),
                'name': channel.name,
                'type': str(channel.type),
                'guild_id': str(channel.guild.id) if hasattr(channel, 'guild') else None,
                'created_at': channel.created_at.isoformat()
            })
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Discord channel information.'}, status=500)

    async def get_discord_invite(self, request):
        invite_code = request.query.get('code')
        if not invite_code:
            return web.json_response({'error': '400', 'message': 'Parameter "code" is required'}, status=400)

        try:
            invite = await self.bot.fetch_invite(invite_code)
            return web.json_response({
                'code': invite.code,
                'guild': {
                    'id': str(invite.guild.id),
                    'name': invite.guild.name,
                    'icon': invite.guild.icon.url if invite.guild.icon else None
                } if invite.guild else None,
                'channel': {
                    'id': str(invite.channel.id),
                    'name': invite.channel.name,
                    'type': str(invite.channel.type)
                } if invite.channel else None,
                'member_count': invite.approximate_member_count,
                'presence_count': invite.approximate_presence_count
            })
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Discord invite information.'}, status=500)

    # Google reverse image search
    async def get_google_reverse(self, request):
        image_url = request.query.get('url')
        if not image_url:
            return web.json_response({'error': '400', 'message': 'Parameter "url" is required'}, status=400)

        try:
            # This would need Google Custom Search API implementation
            return web.json_response({'error': '500', 'message': 'Google reverse image search not implemented'}, status=500)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error performing reverse image search'}, status=500)

    # Roblox endpoint
    async def get_roblox_user(self, request):
        username = request.query.get('username')
        if not username:
            return web.json_response({'error': '400', 'message': 'Parameter "username" is required'}, status=400)

        try:
            # This would need Roblox API implementation
            return web.json_response({'error': '500', 'message': 'Roblox API not implemented'}, status=500)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Roblox user'}, status=500)

    # Valorant endpoints
    async def get_valorant_user(self, request):
        username = request.query.get('username')
        tag = request.query.get('tag')
        if not username or not tag:
            return web.json_response({'error': '400', 'message': 'Parameters "username" and "tag" are required'}, status=400)

        try:
            # This would need Valorant API implementation
            return web.json_response({'error': '500', 'message': 'Valorant API not implemented'}, status=500)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Valorant user'}, status=500)

    async def get_valorant_ranked_user(self, request):
        username = request.query.get('username')
        tag = request.query.get('tag')
        if not username or not tag:
            return web.json_response({'error': '400', 'message': 'Parameters "username" and "tag" are required'}, status=400)

        try:
            # This would need Valorant API implementation
            return web.json_response({'error': '500', 'message': 'Valorant ranked API not implemented'}, status=500)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Valorant ranked user'}, status=500)

    async def get_valorant_ranked_matches(self, request):
        username = request.query.get('username')
        tag = request.query.get('tag')
        if not username or not tag:
            return web.json_response({'error': '400', 'message': 'Parameters "username" and "tag" are required'}, status=400)

        try:
            # This would need Valorant API implementation
            return web.json_response({'error': '500', 'message': 'Valorant matches API not implemented'}, status=500)
        except Exception as e:
            return web.json_response({'error': '500', 'message': 'Error fetching Valorant ranked matches'}, status=500)

    @Cog.listener("on_message")
    async def on_message(self, _):
        if self.bot.is_ready():
            check = await self.bot.db.fetchrow("SELECT timestamp FROM growth ORDER BY timestamp DESC LIMIT 1")
            if not check or check[0] < datetime.now() - timedelta(minutes=1):
                guilds = len(self.bot.guilds)
                users = sum(g.member_count or 0 for g in self.bot.guilds)
                latency = self.bot.latency
                ping = round(latency * 1000) if latency != float('inf') else -1
                await self.bot.db.execute(
                    "INSERT INTO growth (guilds, users, ping, timestamp) VALUES ($1, $2, $3, $4)",
                    guilds, users, ping, datetime.now()
                )

    @commands.command(name="start")
    async def start_command(self, ctx):
        """Restart the bot - restricted to specific roles"""
        allowed_role_ids = [1237426196422328381, 1305786245942743061, 1293528425763704915, 1267401506555166793, 1272482302227910708]
        if not any(role.id in allowed_role_ids for role in ctx.author.roles):
            return await ctx.send("You are not allowed to use this command.")
        os.system("pm2 restart 0")
        return await ctx.send("Trying to restart Evelina")

    async def cog_unload(self):
        if self.api_runner:
            await self.api_runner.cleanup()
        if self.static_runner:
            await self.static_runner.cleanup()

async def setup(bot: Evelina) -> None:
    await bot.add_cog(Api(bot))